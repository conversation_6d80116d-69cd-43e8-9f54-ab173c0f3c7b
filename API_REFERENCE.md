# API 參考文檔 | API Reference

## 📋 目錄

- [組件 API](#組件-api)
- [配置文件格式](#配置文件格式)
- [Canvas 渲染 API](#canvas-渲染-api)
- [數據結構](#數據結構)
- [事件系統](#事件系統)

## 🧩 組件 API

### 主頁面組件 (pages/index.vue)

#### Props
此組件不接受外部 props，所有數據通過內部 data 管理。

#### Data Properties

| 屬性名 | 類型 | 默認值 | 描述 |
|--------|------|--------|------|
| `uiLang` | String | 'zh' | 界面語言代碼 |
| `cardLang` | String | 'zh' | 卡片語言代碼 |
| `holo` | Boolean | true | 是否顯示防偽貼 |
| `cardRare` | String | '0' | 稀有度 (0:N, 1:R, 2:UR) |
| `titleColor` | String | '#000000' | 卡片標題顏色 |
| `cardKey` | String | '' | 8位卡片密碼 |
| `cardTitle` | String | '' | 卡片名稱 |
| `cardImg` | File\|null | null | 上傳的卡片圖片 |
| `cardType` | String | 'Monster' | 卡片類型 |
| `cardSubtype` | String | 'Normal' | 卡片子類型 |
| `cardAttr` | String | 'LIGHT' | 怪獸屬性 |
| `cardRace` | String | 'dragon' | 怪獸種族 |
| `cardLevel` | String | '12' | 怪獸等級/階級 |
| `cardATK` | String | '' | 攻擊力 |
| `cardDEF` | String | '' | 守備力 |
| `Pendulum` | Boolean | true | 是否為靈擺怪獸 |
| `cardBLUE` | Number | 12 | 靈擺藍色刻度 |
| `cardRED` | Number | 12 | 靈擺紅色刻度 |
| `links` | Object | {...} | 連結怪獸的連結標記 |
| `cardInfo` | String | '' | 卡片效果說明 |
| `infoSize` | String | '22' | 說明文字大小 |

#### Computed Properties

| 屬性名 | 返回類型 | 描述 |
|--------|----------|------|
| `cardTemplateText` | String | 卡片模板文件名 |
| `isEffectMonster` | Boolean | 是否為效果怪獸 |
| `isXyzMonster` | Boolean | 是否為超量怪獸 |
| `isLinkMonster` | Boolean | 是否為連結怪獸 |
| `canPendulumEnabled` | Boolean | 是否可以設置靈擺 |
| `cardTypeOpts` | Object | 卡片類型選項 |
| `cardSubtypeOpts` | Object | 卡片子類型選項 |
| `cardAttrOpts` | Array | 屬性選項列表 |
| `cardRaceOpts` | Object | 種族選項 |

#### Methods

##### 卡片繪製相關
```javascript
// 執行卡片繪製（帶加載提示）
doDrawCard(): void

// 主繪製方法
drawCard(): void

// 載入圖片資源
drawCardLoadingImages(callback: Function): void

// Canvas 繪製流程
drawCardProcess(): void

// 繪製卡片圖片和底圖
drawCardImg(ctx: CanvasRenderingContext2D): void

// 繪製卡片標題
drawCardTitle(ctx: CanvasRenderingContext2D, offset: Object, fontName: Array): void

// 繪製卡片信息
drawCardInfo(ctx: CanvasRenderingContext2D, langStr: Object, offset: Object, fontName: Array): void

// 繪製靈擺效果文字
drawCardPendulumInfoText(ctx: CanvasRenderingContext2D, offset: Object, fontName: Array): void

// 繪製卡片說明文字
drawCardInfoText(ctx: CanvasRenderingContext2D, offset: Object, fontName: Array): void

// 文字自動換行
wrapText(ctx: CanvasRenderingContext2D, text: String, x: Number, y: Number, maxWidth: Number, lineHeight: Number): void
```

##### 數據操作相關
```javascript
// 載入預設數據
load_default_data(): void

// 載入 YGOPro 卡片數據
load_ygopro_data(key: String): Boolean

// 下載卡片圖片
download_img(): void
```

##### 視覺效果相關
```javascript
// 3D 懸停效果 - 滑鼠移動
move(e: MouseEvent): void

// 3D 懸停效果 - 滑鼠離開
leave(e: MouseEvent): void

// 頁面滾動處理
onScroll(): void
```

##### 輔助方法
```javascript
// 獲取稀有度顏色
rareColor(ctx: CanvasRenderingContext2D): String|CanvasGradient
```

### 加載對話框組件 (components/LoadingDialog.vue)

#### Computed Properties

| 屬性名 | 類型 | 描述 |
|--------|------|------|
| `show` | Boolean | 控制對話框顯示/隱藏 |

#### Methods

```javascript
// 關閉加載對話框
closeLoadingDialog(): void
```

## ⚙️ 配置文件格式

### 界面語言配置 (static/lang.ui.json)

```typescript
interface UILanguage {
  [languageCode: string]: {
    name: string;                    // 語言顯示名稱
    ui_lang: string;                 // "界面語言" 文字
    card_lang: string;               // "卡片語言" 文字
    card_name: string;               // "卡片名稱" 文字
    monster_card: string;            // "怪獸" 文字
    spell_card: string;              // "魔法" 文字
    trap_card: string;               // "陷阱" 文字
    
    // 怪獸卡類型
    m_card: {
      normal: string;                // "通常"
      effect: string;                // "效果"
      fusion: string;                // "融合"
      ritual: string;                // "儀式"
      synchro: string;               // "同步"
      xyz: string;                   // "超量"
      link: string;                  // "連結"
      token: string;                 // "衍生物"
    };
    
    // 魔法/陷阱卡類型
    st_card: {
      normal: string;                // "通常"
      continuous: string;            // "永續"
      field: string;                 // "場地"
      equip: string;                 // "裝備"
      quick: string;                 // "速攻"
      ritual: string;                // "儀式"
      counter: string;               // "反擊"
    };
    
    // 更多界面文字...
  }
}
```

### 卡片語言配置 (static/lang.card_meta.json)

```typescript
interface CardLanguage {
  [languageCode: string]: {
    name: string;                    // 語言顯示名稱
    
    // 卡片文字內容
    SEP: string;                     // 分隔符
    QUOTE_L: string;                 // 左引號
    QUOTE_R: string;                 // 右引號
    M_SPECIAL: string;               // 特殊召喚文字
    M_EFFECT: string;                // 效果怪獸文字
    M_PENDULUM: string;              // 靈擺怪獸文字
    
    // 子類型文字
    Subtype: {
      [subtype: string]: string;
    };
    
    // 效果類型文字
    Effect: {
      [effect: string]: string;
    };
    
    // 種族文字
    Race: {
      [race: string]: string;
    };
    
    Spell: string;                   // 魔法卡文字
    Trap: string;                    // 陷阱卡文字
    
    // 預設卡片數據
    Default: {
      title: string;                 // 預設標題
      info: string;                  // 預設說明
      pInfo: string;                 // 預設靈擺效果
      size: number;                  // 預設文字大小
      pSize: number;                 // 預設靈擺文字大小
    };
    
    // 渲染配置
    _templateLang: string;           // 模板語言
    _fontName: string[];             // 字體名稱數組
    _offset: {                       // 文字偏移配置
      tS: number;                    // 標題大小偏移
      sS: number;                    // 子標題大小偏移
      tX: number;                    // 標題 X 偏移
      tY: number;                    // 標題 Y 偏移
      sX1: number;                   // 子類型 X 偏移 1
      sX2: number;                   // 子類型 X 偏移 2
      sY1: number;                   // 子類型 Y 偏移 1
      sY2: number;                   // 子類型 Y 偏移 2
      oX: number;                    // 其他文字 X 偏移
      oY: number;                    // 其他文字 Y 偏移
      lh: number;                    // 行高偏移
    };
  }
}
```

### YGOPro 卡片數據格式 (static/ygo/card_data.json)

```typescript
interface YGOProCardData {
  [cardId: string]: {
    rare: string;                    // 稀有度
    color: string;                   // 標題顏色
    title: string;                   // 卡片名稱
    type: [string, string, string, string, boolean, boolean]; // [類型, 子類型, 效果1, 效果2, 靈擺, 特殊召喚]
    attribute: string;               // 屬性
    race: string;                    // 種族
    level: string;                   // 等級
    blue: number;                    // 靈擺藍色刻度
    red: number;                     // 靈擺紅色刻度
    atk: string;                     // 攻擊力
    def: string;                     // 守備力
    link1: boolean;                  // 連結標記 1
    link2: boolean;                  // 連結標記 2
    // ... link3 到 link9
    infoText: string;                // 卡片說明
    size: number;                    // 文字大小
    pendulumText: string;            // 靈擺效果
    pSize: number;                   // 靈擺文字大小
  }
}
```

## 🎨 Canvas 渲染 API

### 渲染上下文配置

```javascript
// Canvas 初始化
const canvas = this.$refs.yugiohcard
const ctx = canvas.getContext('2d')
canvas.width = 1000    // 固定寬度
canvas.height = 1450   // 固定高度
```

### 繪製區域定義

```javascript
// 卡片圖片區域
const cardImageArea = {
  normal: { x: 123, y: 268, width: 754, height: 754 },
  pendulum: { x: 69, y: 255, width: 862, height: 647 }
}

// 文字區域
const textAreas = {
  title: { x: 77, y: 140, maxWidth: 750 },
  info: { x: 75, y: 1095, maxWidth: 825 },
  pendulumInfo: { x: 160, y: 920, maxWidth: 660 }
}
```

### 字體配置

```javascript
// 字體設置格式
ctx.font = `${fontSize}pt ${fontFamily}`

// 常用字體組合
const fonts = {
  title: `${57 + offset.tS}pt ${fontName[0]}, ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`,
  info: `${fontSize}pt ${fontName[2]}, ${fontName[3]}, ${fontName[4]}, ${fontName[5]}`,
  cardkey: `22pt 'cardkey', 'MatrixBoldSmallCaps', ${fontName[2]}`
}
```

## 📊 數據結構

### 連結標記數據結構

```javascript
const links = {
  1: { val: false, symbol: '◤' },  // 左上
  2: { val: false, symbol: '▲' },  // 上
  3: { val: false, symbol: '◥' },  // 右上
  4: { val: false, symbol: '◀' },  // 左
  // 5: 中心位置，不使用
  6: { val: false, symbol: '▶' },  // 右
  7: { val: false, symbol: '◣' },  // 左下
  8: { val: false, symbol: '▼' },  // 下
  9: { val: false, symbol: '◢' }   // 右下
}
```

### 圖片資源映射

```javascript
const imageResources = {
  template: `images/card/${templateLang}/${cardTemplateText}.png`,
  holo: "images/pic/holo.png",
  attr: `images/attr/${templateLang}/${cardAttr}.webp`,
  photo: cardImgUrl || "images/default.jpg",
  levelOrSubtype: `images/pic/${isXyzMonster ? 'Rank' : 'Level'}.webp`,
  link1: "images/pic/LINK1.png",
  // ... 其他連結標記圖片
}
```

## 🎯 事件系統

### 生命週期事件

```javascript
mounted() {
  // 監聽頁面滾動
  window.addEventListener('scroll', this.onScroll)
  
  // 初始化加載
  this.fireLoadingDialog()
  this.load_default_data()
  
  // 設置自動繪製
  setInterval(this.drawCard, 1500)
}

beforeDestroy() {
  // 清理事件監聽
  window.removeEventListener('scroll', this.onScroll)
}
```

### 用戶交互事件

```javascript
// 3D 效果事件
@mousemove="move"
@mouseleave="leave"

// 表單事件
@click="doDrawCard"      // 生成按鈕
@click="download_img"    // 下載按鈕
@click="load_default_data"  // 重置按鈕
```

### Vuex 事件

```javascript
// 觸發加載對話框
this.fireLoadingDialog()

// 關閉加載對話框
this.closeLoadingDialog()
```

## 🔧 擴展接口

### 自定義渲染器接口

```javascript
interface CustomRenderer {
  // 自定義繪製方法
  render(ctx: CanvasRenderingContext2D, data: CardData): void
  
  // 獲取所需資源
  getRequiredImages(): string[]
  
  // 驗證數據
  validateData(data: CardData): boolean
}
```

### 語言包接口

```javascript
interface LanguagePack {
  // 添加新語言
  addLanguage(code: string, config: LanguageConfig): void
  
  // 獲取翻譯
  translate(key: string, lang: string): string
  
  // 驗證語言包
  validate(config: LanguageConfig): boolean
}
```

---

這份 API 參考文檔提供了項目中所有主要接口和數據結構的詳細說明。開發者可以根據這些接口進行擴展開發或集成。

# 貢獻指南 | Contributing Guide

感謝您對遊戲王卡片製造機項目的關注！我們歡迎所有形式的貢獻，包括但不限於代碼、文檔、設計、測試和反饋。

## 📋 目錄

- [如何貢獻](#如何貢獻)
- [開發環境設置](#開發環境設置)
- [代碼規範](#代碼規範)
- [提交指南](#提交指南)
- [Pull Request 流程](#pull-request-流程)
- [問題報告](#問題報告)
- [功能請求](#功能請求)
- [社區準則](#社區準則)

## 🤝 如何貢獻

### 貢獻類型

1. **代碼貢獻**
   - 修復 Bug
   - 添加新功能
   - 性能優化
   - 重構代碼

2. **文檔貢獻**
   - 改進現有文檔
   - 添加使用示例
   - 翻譯文檔
   - API 文檔完善

3. **設計貢獻**
   - UI/UX 改進
   - 新卡片模板設計
   - 圖標和素材
   - 響應式設計優化

4. **測試貢獻**
   - 編寫單元測試
   - 集成測試
   - 瀏覽器兼容性測試
   - 性能測試

5. **其他貢獻**
   - 問題報告
   - 功能建議
   - 社區支持
   - 推廣項目

## 🛠 開發環境設置

### 前置要求

- Node.js >= 14.x
- npm >= 6.x 或 yarn >= 1.x
- Git

### 設置步驟

1. **Fork 項目**
```bash
# 在 GitHub 上 Fork 項目到您的賬戶
```

2. **克隆項目**
```bash
git clone https://github.com/YOUR_USERNAME/yugioh-card-maker.git
cd yugioh-card-maker
```

3. **添加上游倉庫**
```bash
git remote add upstream https://github.com/linziyou0601/yugioh-card-maker.git
```

4. **安裝依賴**
```bash
npm install
```

5. **啟動開發服務器**
```bash
npm run dev
```

6. **驗證設置**
   - 打開 http://localhost:3000
   - 確認頁面正常載入
   - 測試基本功能

### 開發工具推薦

- **IDE**: VS Code
- **擴展**:
  - Vetur (Vue.js 支持)
  - ESLint
  - Prettier
  - GitLens

## 📝 代碼規範

### JavaScript/Vue.js 規範

我們使用 ESLint 和 Prettier 來保持代碼一致性：

```bash
# 檢查代碼規範
npm run lint

# 自動修復
npm run lint -- --fix
```

### 命名規範

1. **文件命名**
   - 組件文件：PascalCase (例：`LoadingDialog.vue`)
   - 頁面文件：kebab-case (例：`card-editor.vue`)
   - 工具文件：camelCase (例：`cardUtils.js`)

2. **變量命名**
   - 變量和函數：camelCase
   - 常量：UPPER_SNAKE_CASE
   - 組件名：PascalCase

3. **CSS 類名**
   - 使用 kebab-case
   - 語義化命名
   - 避免過度嵌套

### 代碼風格

```javascript
// ✅ 好的示例
export default {
  name: 'CardEditor',
  
  data() {
    return {
      cardTitle: '',
      isLoading: false
    }
  },
  
  computed: {
    isValidCard() {
      return this.cardTitle.length > 0
    }
  },
  
  methods: {
    async generateCard() {
      this.isLoading = true
      try {
        await this.drawCard()
      } catch (error) {
        console.error('生成卡片失敗:', error)
      } finally {
        this.isLoading = false
      }
    }
  }
}
```

### 註釋規範

```javascript
/**
 * 繪製卡片圖片到 Canvas
 * @param {CanvasRenderingContext2D} ctx - Canvas 上下文
 * @param {Object} cardData - 卡片數據
 * @param {string} cardData.title - 卡片標題
 * @param {string} cardData.type - 卡片類型
 */
drawCardImage(ctx, cardData) {
  // 設置字體樣式
  ctx.font = '24px Arial'
  
  // 繪製標題
  ctx.fillText(cardData.title, 100, 50)
}
```

## 📤 提交指南

### 提交消息格式

我們使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 提交類型

- `feat`: 新功能
- `fix`: Bug 修復
- `docs`: 文檔更新
- `style`: 代碼格式調整
- `refactor`: 代碼重構
- `perf`: 性能優化
- `test`: 測試相關
- `chore`: 構建工具或輔助工具的變動

### 提交示例

```bash
# 新功能
git commit -m "feat: 添加連結怪獸支持"

# Bug 修復
git commit -m "fix: 修復靈擺卡文字顯示問題"

# 文檔更新
git commit -m "docs: 更新 API 文檔"

# 重構
git commit -m "refactor: 優化 Canvas 渲染邏輯"
```

## 🔄 Pull Request 流程

### 準備工作

1. **同步上游代碼**
```bash
git fetch upstream
git checkout main
git merge upstream/main
```

2. **創建功能分支**
```bash
git checkout -b feature/your-feature-name
```

3. **開發和測試**
```bash
# 開發您的功能
# 運行測試
npm run lint
npm run test  # 如果有測試
```

4. **提交更改**
```bash
git add .
git commit -m "feat: 您的功能描述"
```

5. **推送分支**
```bash
git push origin feature/your-feature-name
```

### 創建 Pull Request

1. **填寫 PR 模板**
   - 清晰描述更改內容
   - 說明更改原因
   - 列出相關 Issue
   - 添加截圖（如適用）

2. **PR 標題格式**
```
feat: 添加新的卡片類型支持
fix: 修復移動端顯示問題
docs: 更新安裝指南
```

3. **PR 描述模板**
```markdown
## 更改描述
簡要描述此 PR 的更改內容。

## 更改類型
- [ ] Bug 修復
- [ ] 新功能
- [ ] 文檔更新
- [ ] 性能優化
- [ ] 其他

## 測試
- [ ] 已在本地測試
- [ ] 已添加/更新測試用例
- [ ] 所有測試通過

## 截圖
如果適用，請添加截圖來說明更改。

## 相關 Issue
關閉 #issue_number
```

### 代碼審查

1. **自我檢查清單**
   - [ ] 代碼符合項目規範
   - [ ] 已添加必要的註釋
   - [ ] 已更新相關文檔
   - [ ] 已測試所有更改
   - [ ] 沒有引入新的警告或錯誤

2. **響應審查意見**
   - 及時回應審查者的意見
   - 進行必要的修改
   - 解釋設計決策（如需要）

## 🐛 問題報告

### 報告 Bug

使用 GitHub Issues 報告 Bug，請包含以下信息：

1. **Bug 描述**
   - 清晰簡潔的問題描述
   - 預期行為 vs 實際行為

2. **重現步驟**
   - 詳細的重現步驟
   - 最小化重現案例

3. **環境信息**
   - 瀏覽器版本
   - 操作系統
   - 設備類型（桌面/移動）

4. **截圖或錄屏**
   - 如果適用，添加視覺證據

### Bug 報告模板

```markdown
**Bug 描述**
簡要描述遇到的問題。

**重現步驟**
1. 進入 '...'
2. 點擊 '....'
3. 滾動到 '....'
4. 看到錯誤

**預期行為**
描述您期望發生的情況。

**截圖**
如果適用，添加截圖來幫助解釋問題。

**環境信息:**
 - 操作系統: [例如 iOS]
 - 瀏覽器: [例如 chrome, safari]
 - 版本: [例如 22]

**其他信息**
添加任何其他相關信息。
```

## 💡 功能請求

### 提出新功能

1. **檢查現有 Issues**
   - 確認功能尚未被提出
   - 查看相關討論

2. **功能描述**
   - 清晰描述功能需求
   - 說明使用場景
   - 提供設計建議（如有）

3. **功能請求模板**
```markdown
**功能描述**
簡要描述您希望添加的功能。

**使用場景**
描述此功能的使用場景和價值。

**解決方案**
描述您希望如何實現此功能。

**替代方案**
描述您考慮過的其他解決方案。

**其他信息**
添加任何其他相關信息或截圖。
```

## 🌟 社區準則

### 行為準則

1. **友善和尊重**
   - 對所有參與者保持友善
   - 尊重不同的觀點和經驗
   - 接受建設性的批評

2. **包容性**
   - 歡迎所有背景的貢獻者
   - 使用包容性語言
   - 避免歧視性言論

3. **專業性**
   - 保持專業的溝通方式
   - 專注於技術討論
   - 避免人身攻擊

### 溝通渠道

- **GitHub Issues**: 問題報告和功能請求
- **GitHub Discussions**: 一般討論和問答
- **Pull Requests**: 代碼審查和討論

### 獲得幫助

如果您需要幫助：

1. 查看現有文檔
2. 搜索相關 Issues
3. 創建新的 Discussion
4. 聯繫維護者

## 🎉 致謝

感謝所有為項目做出貢獻的開發者！您的貢獻讓這個項目變得更好。

### 貢獻者名單

貢獻者將在項目的 README.md 中得到認可。

---

再次感謝您的貢獻！如果您有任何問題，請隨時通過 GitHub Issues 或 Discussions 聯繫我們。

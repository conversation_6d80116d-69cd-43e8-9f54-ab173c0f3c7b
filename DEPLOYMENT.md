# Vercel Deployment Configuration

## 问题解决

### 1. Babel优化问题
- **问题**: Bootstrap Vue图标文件过大导致Babel去优化
- **解决方案**: 
  - 禁用Bootstrap Vue图标 (`icons: false`)
  - 使用树摇优化只导入需要的组件
  - 配置Babel `compact: false`

### 2. 输出目录问题
- **问题**: Vercel找不到`dist`目录
- **解决方案**: 
  - 在`nuxt.config.js`中配置`generate.dir: 'dist'`
  - 在`vercel.json`中指定`outputDirectory: "dist"`

### 3. Node.js版本兼容性
- **问题**: OpenSSL错误
- **解决方案**: 添加`NODE_OPTIONS='--openssl-legacy-provider'`到所有npm脚本

## 配置文件

### vercel.json
```json
{
  "version": 2,
  "builds": [
    {
      "src": "nuxt.config.js",
      "use": "@nuxtjs/vercel-builder"
    }
  ],
  "outputDirectory": "dist"
}
```

### .nvmrc
```
18
```

### 构建命令
- 开发: `npm run dev`
- 构建: `npm run build`
- 生成静态文件: `npm run generate`
- Vercel构建: `npm run vercel-build`

## 部署步骤

1. 确保所有依赖已安装: `npm install`
2. 测试本地构建: `npm run generate`
3. 检查`dist`目录是否生成
4. 推送到Git仓库
5. 在Vercel中连接仓库并部署

## 优化配置

- Bootstrap Vue组件按需导入
- CSS提取和优化
- 代码分割优化
- Babel配置优化
- Webpack bundle大小限制

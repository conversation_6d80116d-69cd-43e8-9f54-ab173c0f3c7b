# 開發者指南 | Developer Guide

## 📋 目錄

- [項目架構](#項目架構)
- [核心模塊](#核心模塊)
- [Canvas 渲染系統](#canvas-渲染系統)
- [多語言系統](#多語言系統)
- [狀態管理](#狀態管理)
- [API 參考](#api-參考)
- [擴展開發](#擴展開發)

## 🏗 項目架構

### 技術選型理由

- **Nuxt.js**：提供 SSG（靜態站點生成）能力，適合部署到 GitHub Pages
- **Vue.js 2.x**：成熟穩定的前端框架，豐富的生態系統
- **Bootstrap Vue**：快速構建響應式界面
- **Canvas API**：高性能的圖像渲染，支持複雜的文字和圖像處理

### 架構設計模式

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用戶界面層     │    │   業務邏輯層     │    │   數據存儲層     │
│  (Vue Components)│    │ (Canvas Render) │    │ (Static JSON)   │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ - 表單控件       │    │ - 卡片繪製       │    │ - 語言配置       │
│ - 實時預覽       │    │ - 圖像處理       │    │ - 卡片數據       │
│ - 用戶交互       │    │ - 文字渲染       │    │ - 模板資源       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🧩 核心模塊

### 1. 主頁面組件 (pages/index.vue)

#### 數據結構
```javascript
data() {
  return {
    // 界面控制
    uiLang: 'zh',           // 界面語言
    cardLang: 'zh',         // 卡片語言
    
    // 卡片基本屬性
    holo: true,             // 防偽貼
    cardRare: '0',          // 稀有度
    titleColor: '#000000',  // 標題顏色
    cardKey: '',            // 卡片密碼
    cardTitle: '',          // 卡片名稱
    cardImg: null,          // 卡片圖片
    
    // 卡片類型
    cardType: 'Monster',    // 卡種
    cardSubtype: 'Normal',  // 卡面
    cardEff1: 'normal',     // 效果1
    cardEff2: 'none',       // 效果2
    
    // 怪獸屬性
    cardAttr: 'LIGHT',      // 屬性
    cardRace: 'dragon',     // 種族
    cardLevel: '12',        // 等級
    cardATK: '',            // 攻擊力
    cardDEF: '',            // 守備力
    
    // 靈擺屬性
    Pendulum: true,         // 靈擺開關
    cardBLUE: 12,           // 藍色刻度
    cardRED: 12,            // 紅色刻度
    cardPendulumInfo: '',   // 靈擺效果
    
    // 連結屬性
    links: {                // 連結標記
      1: {val: false, symbol: '◤'},
      2: {val: false, symbol: '▲'},
      // ... 其他方向
    },
    
    // 文字內容
    cardInfo: '',           // 卡片說明
    infoSize: '22',         // 文字大小
  }
}
```

#### 核心計算屬性
```javascript
computed: {
  // 卡片模板路徑
  cardTemplateText() {
    let templateUrl = this.cardType !== "Monster" ? this.cardType : this.cardSubtype
    if (this.Pendulum && !["Slifer", "Ra", "Obelisk", "LDragon"].includes(this.cardSubtype))
      templateUrl += "Pendulum"
    return templateUrl
  },
  
  // 是否為效果怪獸
  isEffectMonster() {
    return this.cardSubtype === "Effect" || (this.cardEff2 !== "none" && this.cardSubtype !== "Normal")
  },
  
  // 是否為連結怪獸
  isLinkMonster() {
    return this.cardType === 'Monster' && this.cardSubtype === 'Link'
  }
}
```

### 2. 加載對話框組件 (components/LoadingDialog.vue)

```javascript
// 使用 Vuex 管理加載狀態
computed: {
  ...mapGetters(['loadingDialogShow']),
  show: {
    get() { return this.loadingDialogShow },
    set(_) { this.closeLoadingDialog() }
  }
}
```

## 🎨 Canvas 渲染系統

### 渲染流程

```javascript
// 1. 主渲染方法
drawCard() {
  // 準備圖片資源
  this.imgs = {
    template: `images/card/${templateLang}/${this.cardTemplateText}.png`,
    holo: "images/pic/holo.png",
    attr: `images/attr/${templateLang}/${this.cardAttr}.webp`,
    photo: cardImgUrl || "images/default.jpg",
    // ... 其他資源
  }
  
  // 載入圖片後執行繪製
  this.drawCardLoadingImages(this.drawCardProcess)
}

// 2. 圖片載入
drawCardLoadingImages(callback) {
  const length = Object.keys(this.imgs).length
  let count = 0
  
  for (const key in this.imgs) {
    const image = new window.Image()
    image.src = this.imgs[key]
    this.imgs[key] = image
    this.imgs[key].onload = function() {
      count += 1
      if (count >= length) setTimeout(callback, 200)
    }
  }
}

// 3. 主繪製流程
drawCardProcess() {
  const canvas = this.$refs.yugiohcard
  const ctx = canvas.getContext('2d')
  canvas.width = 1000
  canvas.height = 1450
  
  // 繪製各個部分
  this.drawCardImg(ctx)        // 底圖和卡片圖片
  this.drawCardTitle(ctx, offset, fontName)  // 標題
  this.drawCardInfo(ctx, langStr, offset, fontName)  // 卡片信息
  this.drawCardInfoText(ctx, offset, fontName)  // 說明文字
  
  if (this.Pendulum) {
    this.drawCardPendulumInfoText(ctx, offset, fontName)  // 靈擺效果
  }
}
```

### 關鍵渲染方法

#### 圖片繪製
```javascript
drawCardImg(ctx) {
  let cX, cY, cW, cH
  if (this.Pendulum) { 
    cX = 69; cY = 255; cW = 862; cH = 647  // 靈擺卡尺寸
  } else { 
    cX = 123; cY = 268; cW = 754; cH = 754  // 普通卡尺寸
  }
  
  const photo = this.imgs.photo
  const iW = photo.width / photo.height * cH
  const iH = photo.height / photo.width * cW
  
  // 根據圖片比例選擇縮放方式
  if (photo.width <= photo.height * (this.Pendulum ? 1.33 : 1)) {
    ctx.drawImage(photo, cX, cY - ((iH - cH) / 2), cW, iH)
  } else {
    ctx.drawImage(photo, cX - ((iW - cW) / 2), cY, iW, cH)
  }
  
  // 繪製模板和屬性圖標
  ctx.drawImage(this.imgs.template, 0, 0, 1000, 1450)
  ctx.drawImage(this.imgs.attr, 840, 68, 90, 90)
}
```

#### 文字渲染
```javascript
wrapText(ctx, text, x, y, maxWidth, lineHeight) {
  let lineWidth = 0 - ctx.measureText(text[0]).width
  const fieldWidth = maxWidth
  let initHeight = y
  let lastSubStrIndex = 0
  
  for (let i = 0; i < text.length; i++) {
    lineWidth += ctx.measureText(text[i]).width
    
    if (lineWidth > fieldWidth || text.substring(i, i + 1) === '\n') {
      if (text.substring(i, i + 1) === '\n') i++
      ctx.fillText(text.substring(lastSubStrIndex, i), x, initHeight)
      initHeight += lineHeight
      lineWidth = 0
      lastSubStrIndex = i
    }
    
    if (i === text.length - 1) {
      ctx.fillText(text.substring(lastSubStrIndex, i + 1), x, initHeight)
    }
  }
}
```

## 🌍 多語言系統

### 界面語言配置 (static/lang.ui.json)

```json
{
  "zh": {
    "name": "正體中文 (T.Chinese)",
    "card_name": "卡片名稱",
    "monster_card": "怪獸",
    "m_card": {
      "normal": "通常",
      "effect": "效果",
      "fusion": "融合"
    }
  },
  "en": {
    "name": "English",
    "card_name": "Card Name",
    "monster_card": "Monster",
    "m_card": {
      "normal": "Normal",
      "effect": "Effect", 
      "fusion": "Fusion"
    }
  }
}
```

### 卡片語言配置 (static/lang.card_meta.json)

```json
{
  "zh": {
    "name": "正體中文",
    "_templateLang": "zh",
    "_fontName": ["zh", "zh", "zh", "zh", "cn", "zh"],
    "_offset": {
      "tS": 0, "sS": 0,           // 標題和子標題大小偏移
      "tX": 0, "tY": 0,           // 標題位置偏移
      "sX1": 0, "sX2": 0,         // 子類型位置偏移
      "sY1": 0, "sY2": 0,
      "oX": 0, "oY": 0,           // 其他文字偏移
      "lh": 5                     // 行高偏移
    },
    "Race": {
      "dragon": "龍族",
      "warrior": "戰士族"
    },
    "Default": {
      "title": "異色眼革命龍",
      "info": "這張卡不能通常召喚...",
      "pInfo": "靈擺效果：只能靈擺召喚龍族怪獸...",
      "size": 22,
      "pSize": 23
    }
  }
}
```

### 字體系統

字體配置在 `static/fonts/font-face.css` 中定義：

```css
@font-face {
  font-family: 'zh';
  src: url('./zh.ttf') format('truetype');
}

@font-face {
  font-family: 'en';
  src: url('./en.ttf') format('truetype');
}

@font-face {
  font-family: 'MatrixBoldSmallCaps';
  src: url('./MatrixBoldSmallCaps.ttf') format('truetype');
}
```

## 📊 狀態管理

### Vuex Store (store/index.js)

```javascript
const state = () => ({
  _loadingDialogShow: false,
})

const getters = {
  loadingDialogShow: (state) => state._loadingDialogShow,
}

const mutations = {
  fireLoadingDialog(state) {
    state._loadingDialogShow = true
  },
  closeLoadingDialog(state) {
    state._loadingDialogShow = false
  },
}
```

## 🔌 API 參考

### 主要方法

#### 卡片數據操作
```javascript
// 載入預設數據
load_default_data()

// 載入 YGOPro 數據
load_ygopro_data(key)

// 下載卡片圖片
download_img()
```

#### Canvas 操作
```javascript
// 執行卡片繪製
doDrawCard()

// 自動繪製（定時器）
drawCard()

// 繪製流程控制
drawCardProcess()
```

#### 3D 效果
```javascript
// 滑鼠移動效果
move(e)

// 滑鼠離開效果  
leave(e)
```

### 事件監聽

```javascript
mounted() {
  // 監聽頁面滾動
  window.addEventListener('scroll', this.onScroll)
  
  // 啟動加載對話框
  this.fireLoadingDialog()
  
  // 載入預設數據
  this.load_default_data()
  
  // 設置自動繪製定時器
  setInterval(this.drawCard, 1500)
}
```

## 🚀 擴展開發

### 添加新卡片類型

1. **更新語言文件**：在 `lang.ui.json` 和 `lang.card_meta.json` 中添加新類型的翻譯

2. **準備模板圖片**：創建對應的卡片模板圖片

3. **修改組件邏輯**：
```javascript
// 在 cardSubtypeOpts 中添加新選項
cardSubtypeOpts() {
  return {
    "Monster": {
      'Normal': this.ui[this.uiLang].m_card.normal,
      'NewType': this.ui[this.uiLang].m_card.newtype,  // 新增
    }
  }
}

// 在繪製邏輯中處理新類型
drawCardInfo(ctx, langStr, offset, fontName) {
  if (this.cardSubtype === 'NewType') {
    // 新類型的特殊繪製邏輯
  }
}
```

### 添加新語言支持

1. **準備字體文件**：將字體文件放入 `static/fonts/` 目錄

2. **更新字體配置**：在 `font-face.css` 中定義新字體

3. **添加語言配置**：
```json
{
  "new_lang": {
    "name": "New Language",
    "_templateLang": "en",
    "_fontName": ["new_font", "new_font", "new_font", "zh", "cn", "en"],
    "_offset": {
      "tS": 0, "sS": 0,
      "tX": 0, "tY": 0,
      "sX1": 0, "sX2": 0,
      "sY1": 0, "sY2": 0,
      "oX": 0, "oY": 0,
      "lh": 5
    }
  }
}
```

### 性能優化建議

1. **圖片預載入**：在應用啟動時預載入常用圖片資源
2. **Canvas 緩存**：對不變的部分使用離屏 Canvas 緩存
3. **防抖處理**：對頻繁觸發的繪製操作添加防抖
4. **懶加載**：對大量卡片數據實現懶加載

### 調試技巧

1. **Canvas 調試**：
```javascript
// 在繪製方法中添加調試信息
console.log('Drawing at:', x, y, width, height)

// 繪製調試框架
ctx.strokeStyle = 'red'
ctx.strokeRect(x, y, width, height)
```

2. **性能監控**：
```javascript
// 測量繪製時間
const startTime = performance.now()
this.drawCardProcess()
const endTime = performance.now()
console.log(`繪製耗時: ${endTime - startTime}ms`)
```

---

這份開發者指南涵蓋了項目的核心架構和主要功能模塊。如需更詳細的信息，請參考源代碼中的註釋或聯繫項目維護者。

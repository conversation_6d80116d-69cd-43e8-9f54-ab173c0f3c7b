# 故障排除指南 | Troubleshooting Guide

## 🚨 常見問題和解決方案

### 1. `_vm.$t is not a function` 錯誤

**問題描述**: 頁面顯示 `_vm.$t is not a function` 錯誤

**原因**: 使用了 `$t` 函數但沒有配置 i18n 插件

**解決方案**: 
- ✅ 已修復：移除了所有 `$t` 函數的使用
- ✅ 使用 Vuex 狀態管理替代 i18n

### 2. `currentUIData is undefined` 錯誤

**問題描述**: 組件中 `currentUIData` 為 undefined

**原因**: Vuex store 初始化時機問題

**解決方案**:
- ✅ 已修復：在所有使用 `currentUIData` 的地方添加了安全檢查
- ✅ 使用 `(currentUIData && currentUIData.property) || 'fallback'` 模式

### 3. Font Awesome 圖標不顯示

**問題描述**: 頁面中的圖標不顯示

**原因**: Font Awesome 配置問題

**解決方案**:
- ✅ 已修復：使用項目現有的 `nuxt-fontawesome` 配置
- ✅ 確保所有使用的圖標都在 nuxt.config.js 中正確配置

### 4. Vuex Store 導出問題

**問題描述**: Store 模塊導出格式錯誤

**原因**: 使用了錯誤的導出格式

**解決方案**:
- ✅ 已修復：使用 Nuxt.js 標準的 store 導出格式
- ✅ 使用 `export const state`, `export const getters` 等

## 🔧 當前狀態檢查

### 已修復的問題
- [x] `$t` 函數錯誤
- [x] `currentUIData` undefined 錯誤
- [x] Vuex store 導出格式
- [x] Font Awesome 配置
- [x] SEO head 函數安全性

### 當前功能狀態
- [x] 頁面能正常載入
- [x] 語言切換組件正常顯示
- [x] 新增的頁面區塊正常顯示
- [x] 原有的卡片生成功能保持正常
- [x] 響應式設計正常工作

## 🧪 測試步驟

### 基本功能測試
1. **頁面載入測試**
   ```bash
   npm run dev
   # 訪問 http://localhost:3000
   # 確認頁面正常載入，無 JavaScript 錯誤
   ```

2. **語言切換測試**
   - 點擊右上角語言切換器
   - 確認能正常切換中文、英文、日文
   - 確認頁面內容同步更新

3. **卡片生成測試**
   - 滾動到卡片編輯器區域
   - 修改卡片設置
   - 確認卡片預覽正常更新
   - 測試下載功能

4. **響應式測試**
   - 調整瀏覽器窗口大小
   - 確認在不同屏幕尺寸下顯示正常
   - 測試移動端體驗

### SEO 功能測試
1. **Meta 標籤測試**
   - 查看頁面源代碼
   - 確認 title、description、keywords 等標籤正確
   - 測試語言切換時 meta 標籤更新

2. **結構化數據測試**
   - 使用 Google 結構化數據測試工具
   - 確認 JSON-LD 數據正確

## 🔍 調試技巧

### 瀏覽器開發者工具
1. **控制台檢查**
   ```javascript
   // 檢查 Vuex 狀態
   $nuxt.$store.state
   
   // 檢查當前語言
   $nuxt.$store.getters.currentLanguage
   
   // 檢查 UI 數據
   $nuxt.$store.getters.currentUIData
   ```

2. **Vue DevTools**
   - 安裝 Vue DevTools 瀏覽器擴展
   - 檢查組件狀態和 props
   - 監控 Vuex 狀態變化

### 服務器端調試
1. **開發服務器日誌**
   ```bash
   npm run dev
   # 查看控制台輸出，注意錯誤和警告
   ```

2. **構建測試**
   ```bash
   npm run build
   npm run start
   # 測試生產環境構建
   ```

## 🚀 性能優化建議

### 當前性能狀態
- ✅ 使用 Nuxt.js SSG 模式
- ✅ 圖片和字體資源優化
- ✅ CSS 和 JavaScript 壓縮
- ✅ 懶加載和代碼分割

### 進一步優化
1. **圖片優化**
   - 使用 WebP 格式
   - 實現圖片懶加載
   - 添加圖片壓縮

2. **字體優化**
   - 使用 font-display: swap
   - 預加載關鍵字體
   - 字體子集化

3. **JavaScript 優化**
   - 移除未使用的代碼
   - 實現組件懶加載
   - 優化 Bundle 大小

## 📞 獲取幫助

### 如果遇到問題
1. **檢查控制台錯誤**
   - 打開瀏覽器開發者工具
   - 查看 Console 標籤頁
   - 記錄具體錯誤信息

2. **檢查網絡請求**
   - 查看 Network 標籤頁
   - 確認所有資源正常載入
   - 檢查 API 請求狀態

3. **檢查 Vue 組件狀態**
   - 使用 Vue DevTools
   - 檢查組件 data 和 computed 屬性
   - 監控 Vuex 狀態變化

### 常用調試命令
```bash
# 清除緩存重新安裝
rm -rf node_modules package-lock.json
npm install

# 檢查 ESLint 錯誤
npm run lint

# 修復 ESLint 錯誤
npm run lint -- --fix

# 分析 Bundle 大小
npm run build -- --analyze
```

## ✅ 確認清單

在部署前，請確認以下項目：

### 功能測試
- [ ] 頁面正常載入，無 JavaScript 錯誤
- [ ] 語言切換功能正常工作
- [ ] 卡片生成功能正常工作
- [ ] 所有按鈕和鏈接可點擊
- [ ] 響應式設計在各設備上正常

### SEO 測試
- [ ] 頁面標題正確顯示
- [ ] Meta 描述完整
- [ ] 結構化數據有效
- [ ] 多語言標籤正確

### 性能測試
- [ ] 頁面載入速度快（< 3秒）
- [ ] 圖片正常載入
- [ ] 字體正常顯示
- [ ] 無控制台錯誤或警告

---

**注意**: 如果遇到任何問題，請先檢查瀏覽器控制台的錯誤信息，這通常能提供解決問題的關鍵線索。

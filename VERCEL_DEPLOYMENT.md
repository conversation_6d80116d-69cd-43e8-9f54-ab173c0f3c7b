# Vercel 部署指南

## 🚀 快速部署

### 方法一：通过 Vercel Dashboard

1. **登录 Vercel**
   - 访问 [vercel.com](https://vercel.com)
   - 使用 GitHub 账户登录

2. **导入项目**
   - 点击 "New Project"
   - 选择您的 GitHub 仓库
   - 点击 "Import"

3. **配置设置**
   - Framework Preset: 选择 "Other" 或留空
   - Build Command: `npm run vercel-build`
   - Output Directory: `dist`
   - Install Command: `npm ci`
   - Node.js Version: `18.x` (推荐)

4. **部署**
   - 点击 "Deploy"
   - 等待构建完成

### 方法二：通过 Vercel CLI

```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录
vercel login

# 部署
vercel --prod
```

## 📋 配置文件说明

### vercel.json
```json
{
  "version": 2,
  "buildCommand": "npm run vercel-build",
  "outputDirectory": "dist",
  "installCommand": "npm ci",
  "framework": null
}
```

### package.json 关键配置
```json
{
  "scripts": {
    "vercel-build": "NODE_OPTIONS='--openssl-legacy-provider' nuxt generate"
  },
  "engines": {
    "node": ">=16.0.0",
    "npm": ">=7.0.0"
  }
}
```

## ⚠️ 重要注意事项

1. **包管理器**
   - 项目使用 npm，已删除 `pnpm-lock.yaml`
   - 确保只有 `package-lock.json` 存在

2. **Node.js 版本**
   - 推荐使用 Node.js 18.x
   - 项目使用 `--openssl-legacy-provider` 标志

3. **构建命令**
   - 使用 `npm run vercel-build` 而不是 `npm run build`
   - 这会生成静态文件到 `dist` 目录

## 🔧 故障排除

### 常见问题

1. **pnpm 错误**
   - 确保删除了 `pnpm-lock.yaml`
   - 使用 `npm ci` 安装依赖

2. **构建失败**
   - 检查 Node.js 版本是否兼容
   - 确保所有依赖都已正确安装

3. **路由问题**
   - 项目配置为 SPA 模式
   - 所有路由都会回退到 `index.html`

### 环境变量

如果需要环境变量，在 Vercel Dashboard 中设置：
- 进入项目设置
- 选择 "Environment Variables"
- 添加所需的变量

## 📊 性能优化

项目已包含以下优化：

1. **静态资源缓存**
   - 字体文件：1年缓存
   - 图片文件：1年缓存
   - JS/CSS 文件：1年缓存

2. **代码分割**
   - 自动分割 JavaScript 包
   - 按需加载组件

3. **压缩优化**
   - CSS 和 JS 文件自动压缩
   - 图片优化

## 🌐 自定义域名

部署成功后，可以配置自定义域名：

1. 在 Vercel Dashboard 中选择项目
2. 进入 "Settings" > "Domains"
3. 添加您的域名
4. 按照指示配置 DNS

## 📈 监控和分析

Vercel 提供内置的分析功能：
- 访问统计
- 性能监控
- 错误追踪

在项目 Dashboard 中查看 "Analytics" 选项卡。

## 🔄 自动部署

项目已配置自动部署：
- 推送到 main 分支时自动部署
- Pull Request 会创建预览部署
- 支持回滚到之前的版本

## 📞 支持

如果遇到问题：
1. 检查 Vercel 构建日志
2. 查看本地构建是否成功
3. 确认配置文件正确
4. 联系 Vercel 支持团队

// Google Analytics configuration - Updated tracking ID G-PN1XZ4X9VG
// Removed environment condition to ensure GA works in all environments
const gaTags = [
  {
    hid: 'gtag-script',
    src: 'https://www.googletagmanager.com/gtag/js?id=G-PN1XZ4X9VG',
    async: true
  },
  {
    hid: 'gtag-config',
    innerHTML: `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-PN1XZ4X9VG', {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {'custom_parameter': 'yugioh_card_maker'}
      });
    `,
    type: 'text/javascript',
    charset: 'utf-8'
  }
]

export default {
  // Disable server-side rendering: https://go.nuxtjs.dev/ssr-mode
  ssr: false,

  // Target: https://go.nuxtjs.dev/config-target
  target: 'static',

  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: '遊戲王卡片製造機 - 免費在線卡片設計工具',
    titleTemplate: '%s | Yu-Gi-Oh! Card Maker',
    htmlAttrs: {
      lang: 'zh'
    },
    meta: [
      // Basic meta tags
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1, shrink-to-fit=no' },
      { name: 'format-detection', content: 'telephone=no' },
      { name: 'theme-color', content: '#2c3e50' },

      // SEO meta tags
      { hid: 'description', name: 'description', content: '專業的遊戲王卡片製作工具，支持怪獸卡、魔法卡、陷阱卡等所有類型。免費在線使用，支持靈擺、連結怪獸，高質量圖片下載。' },
      { hid: 'keywords', name: 'keywords', content: '遊戲王,卡片製作,卡片設計,遊戲王製造機,Yu-Gi-Oh,卡片生成器,免費工具,怪獸卡,魔法卡,陷阱卡' },
      { hid: 'author', name: 'author', content: 'yugiohcardmaker.org' },
      { hid: 'robots', name: 'robots', content: 'index, follow' },
      { hid: 'googlebot', name: 'googlebot', content: 'index, follow' },

      // Open Graph meta tags
      { hid: 'og:type', property: 'og:type', content: 'website' },
      { hid: 'og:site_name', property: 'og:site_name', content: '遊戲王卡片製造機' },
      { hid: 'og:title', property: 'og:title', content: '遊戲王卡片製造機 - 創造屬於你的卡片' },
      { hid: 'og:description', property: 'og:description', content: '使用我們的免費工具設計專屬的遊戲王卡片，支持所有卡片類型和高級功能。' },
      { hid: 'og:url', property: 'og:url', content: 'https://yugiohcardmaker.org/' },
      { hid: 'og:image', property: 'og:image', content: 'https://yugiohcardmaker.org/images/og-image.jpg' },
      { hid: 'og:image:width', property: 'og:image:width', content: '1200' },
      { hid: 'og:image:height', property: 'og:image:height', content: '630' },
      { hid: 'og:locale', property: 'og:locale', content: 'zh_TW' },
      { hid: 'og:locale:alternate', property: 'og:locale:alternate', content: 'en_US' },
      { hid: 'og:locale:alternate', property: 'og:locale:alternate', content: 'ja_JP' },

      // Twitter Card meta tags
      { hid: 'twitter:card', name: 'twitter:card', content: 'summary_large_image' },
      { hid: 'twitter:site', name: 'twitter:site', content: '@yugiohcardmaker' },
      { hid: 'twitter:creator', name: 'twitter:creator', content: '@yugiohcardmaker' },
      { hid: 'twitter:title', name: 'twitter:title', content: '遊戲王卡片製造機' },
      { hid: 'twitter:description', name: 'twitter:description', content: '免費的遊戲王卡片設計工具，輕鬆創建專業品質的卡片。' },
      { hid: 'twitter:image', name: 'twitter:image', content: 'https://yugiohcardmaker.org/images/twitter-card.jpg' },

      // Additional SEO meta tags
      { hid: 'application-name', name: 'application-name', content: '遊戲王卡片製造機' },
      { hid: 'apple-mobile-web-app-title', name: 'apple-mobile-web-app-title', content: '遊戲王卡片製造機' },
      { hid: 'apple-mobile-web-app-capable', name: 'apple-mobile-web-app-capable', content: 'yes' },
      { hid: 'apple-mobile-web-app-status-bar-style', name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
      { hid: 'msapplication-TileColor', name: 'msapplication-TileColor', content: '#2c3e50' },
      { hid: 'msapplication-config', name: 'msapplication-config', content: '/browserconfig.xml' }
    ],
    link: [
      // Favicon and app icons
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
      { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
      { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
      { rel: 'manifest', href: '/site.webmanifest' },

      // Preconnect to external domains for performance
      { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true },

      // Canonical URL
      { rel: 'canonical', href: 'https://yugiohcardmaker.org/' },

      // Alternate language versions - will be handled by i18n module
      { rel: 'alternate', hreflang: 'en', href: 'https://yugiohcardmaker.org/' },
      { rel: 'alternate', hreflang: 'zh', href: 'https://yugiohcardmaker.org/zh/' },
      { rel: 'alternate', hreflang: 'ja', href: 'https://yugiohcardmaker.org/ja/' },
      { rel: 'alternate', hreflang: 'x-default', href: 'https://yugiohcardmaker.org/' }
    ],
    script: [
      ...gaTags,
      // Structured data for SEO
      {
        hid: 'structured-data',
        type: 'application/ld+json',
        innerHTML: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'WebApplication',
          name: '遊戲王卡片製造機',
          alternateName: 'Yu-Gi-Oh! Card Maker',
          description: '專業的遊戲王卡片製作工具，支持怪獸卡、魔法卡、陷阱卡等所有類型。',
          url: 'https://yugiohcardmaker.org/',
          applicationCategory: 'DesignApplication',
          operatingSystem: 'Web Browser',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD'
          },
          author: {
            '@type': 'Organization',
            name: 'yugiohcardmaker.org',
            url: 'https://yugiohcardmaker.org'
          },
          inLanguage: ['zh', 'en', 'ja'],
          isAccessibleForFree: true,
          screenshot: 'https://yugiohcardmaker.org/images/screenshot.jpg'
        })
      }
    ],
    __dangerouslyDisableSanitizersByTagID: {
      'structured-data': ['innerHTML']
    }
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
    'bootstrap/dist/css/bootstrap.css',
    'bootstrap-vue/dist/bootstrap-vue.css'
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    {
      src: '~/plugins/font-awesome'
    },
    {
      src: '~/plugins/gtag',
      mode: 'client'
    }
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',

    'nuxt-font-loader',
  ],

  
  fontLoader : { 
    url: {
      local: 'fonts/font-face.css',
      google: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700;900&family=Noto+Sans+SC:wght@100;300;400;500;700;900&family=Noto+Sans+TC:wght@100;300;400;500;700;900&display=swap',
    },
    prefetch : true,
    preconnect : true,
    preload: {
      hid: 'my-font-preload',
    },
  },

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    // https://go.nuxtjs.dev/bootstrap
    'bootstrap-vue/nuxt',
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',

    'nuxt-fontawesome',

    // Internationalization
    '@nuxtjs/i18n',

    // Sitemap generation - 禁用自动生成，使用自定义脚本
    // '@nuxtjs/sitemap',
  ],

  // Bootstrap Vue configuration
  bootstrapVue: {
    // Install the `IconsPlugin` plugin (in addition to `BootstrapVue` plugin)
    icons: false, // Disable icons to reduce bundle size
    // Tree-shake bootstrap-vue
    componentPlugins: [
      'LayoutPlugin',
      'FormPlugin',
      'FormCheckboxPlugin',
      'FormInputPlugin',
      'FormSelectPlugin',
      'FormTextareaPlugin',
      'FormFilePlugin',
      'ButtonPlugin',
      'CardPlugin',
      'ModalPlugin',
      'NavbarPlugin',
      'CollapsePlugin',
      'SpinnerPlugin'
    ],
    directivePlugins: [],
    // Disable CSS import to reduce bundle size
    bootstrapCSS: false,
    bootstrapVueCSS: false
  },

  fontawesome: {
    // icon 的標籤使用 <fa>，這邊不設定就會依照 plugin 裡的設定<font-awesome-icon>
    component: 'fa', 
    imports: [
      {
        set: '@fortawesome/free-solid-svg-icons',
        icons: ['fas']
      },
      {
        set: '@fortawesome/free-regular-svg-icons',
        icons: ['far']
      },
      {
        set: '@fortawesome/free-brands-svg-icons',
        icons: ['fab']
      },
    ]
  },

  // Axios module configuration: https://go.nuxtjs.dev/config-axios
  axios: {},

  // i18n configuration
  i18n: {
    locales: [
      {
        code: 'en',
        iso: 'en-US',
        name: 'English',
        file: 'en.js'
      },
      {
        code: 'zh',
        iso: 'zh-TW',
        name: '中文',
        file: 'zh.js'
      },
      {
        code: 'ja',
        iso: 'ja-JP',
        name: '日本語',
        file: 'ja.js'
      }
    ],
    defaultLocale: 'en',
    strategy: 'prefix_except_default',
    langDir: 'locales/',
    lazy: true,
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: false,
      fallbackLocale: 'en'
    },
    seo: true,
    baseUrl: 'https://yugiohcardmaker.org'
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    // Optimize for production builds
    extractCSS: true,

    // Enable CSS optimization
    optimizeCSS: true,

    // Enable HTML minification
    html: {
      minify: {
        collapseBooleanAttributes: true,
        decodeEntities: true,
        minifyCSS: true,
        minifyJS: true,
        processConditionalComments: true,
        removeEmptyAttributes: true,
        removeRedundantAttributes: true,
        trimCustomFragments: true,
        useShortDoctype: true
      }
    },

    // Optimize bundle splitting
    splitChunks: {
      layouts: true,
      pages: true,
      commons: true
    },

    // Babel configuration to handle large files
    babel: {
      compact: false,
      presets({ isServer }) {
        return [
          [
            require.resolve('@nuxt/babel-preset-app'),
            {
              buildTarget: isServer ? 'server' : 'client',
              corejs: { version: 3 }
            }
          ]
        ]
      }
    },

    // Webpack optimization
    extend(config, { isDev, isClient }) {
      // Optimize for production
      if (!isDev && isClient) {
        config.optimization.splitChunks.maxSize = 200000

        // Enable tree shaking
        config.optimization.usedExports = true
        config.optimization.sideEffects = false

        // Optimize images
        config.module.rules.push({
          test: /\.(png|jpe?g|gif|svg|webp)$/i,
          use: [
            {
              loader: 'file-loader',
              options: {
                name: 'img/[name].[hash:8].[ext]'
              }
            }
          ]
        })
      }

      // Handle large dependencies
      config.resolve.alias = {
        ...config.resolve.alias,
        'bootstrap-vue$': 'bootstrap-vue/dist/bootstrap-vue.esm.js'
      }
    },

    // Transpile specific packages
    transpile: [
      'bootstrap-vue'
    ]
  },

  // Generate configuration for static deployment
  generate: {
    dir: 'dist',
    fallback: true
  },

  // Render configuration for performance
  render: {
    // Enable compression
    compressor: { threshold: 0 },

    // Resource hints
    resourceHints: true,

    // HTTP2 push
    http2: {
      push: true,
      pushAssets: (req, res, publicPath, preloadFiles) => {
        return preloadFiles
          .filter(f => f.asType === 'script' && f.file === 'runtime.js')
          .map(f => `<${publicPath}${f.file}>; rel=preload; as=${f.asType}`)
      }
    }
  },

  // Sitemap configuration - 已禁用，使用自定义脚本生成
  /*
  sitemap: {
    hostname: 'https://yugiohcardmaker.org',
    gzip: true,
    exclude: [
      '/analytics-test',
      '/privacy',
      '/terms'
    ],
    i18n: true,
    routes: [
      {
        url: '/',
        changefreq: 'weekly',
        priority: 1.0,
        lastmod: new Date().toISOString()
      }
    ]
  }
  */
}

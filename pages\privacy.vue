<template>
  <div class="privacy-page">
    <!-- Navigation Header -->
    <header role="banner">
      <b-navbar type="dark" fixed="top" class="main-navbar" expand="lg">
        <b-navbar-brand href="/" class="navbar-brand-custom">
          <h1 class="brand-title mb-0">
            <span class="brand-main">Yu-Gi-Oh! Card Maker</span>
            <small class="brand-sub d-block">遊戲王卡片製造機</small>
          </h1>
        </b-navbar-brand>
        
        <!-- Language Switcher -->
        <b-navbar-nav class="ml-auto">
          <LanguageSwitcher />
        </b-navbar-nav>
      </b-navbar>
    </header>

    <!-- Main Content -->
    <main class="privacy-content">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-8">
            <div class="content-wrapper">
              <h1 class="page-title">Privacy Policy</h1>
              <p class="last-updated">Last updated: {{ new Date().toLocaleDateString() }}</p>

              <section class="policy-section">
                <h2>1. Information We Collect</h2>
                <p>yugiohcardmaker.org is committed to protecting your privacy. We collect minimal information to provide our services:</p>
                <ul>
                  <li><strong>Usage Data:</strong> We may collect anonymous usage statistics through Google Analytics to improve our service, including page views, session duration, and general geographic location</li>
                  <li><strong>Local Storage:</strong> Your language preferences and card designs are stored locally in your browser using localStorage technology</li>
                  <li><strong>Uploaded Images:</strong> Images you upload for card creation are processed locally in your browser and are not transmitted to our servers</li>
                  <li><strong>No Personal Data:</strong> We do not collect, store, or process any personally identifiable information such as names, email addresses, or contact details</li>
                </ul>
              </section>

              <section class="policy-section">
                <h2>2. How We Use Information</h2>
                <p>The limited information we collect is used solely to:</p>
                <ul>
                  <li>Provide and maintain our card creation service</li>
                  <li>Improve user experience and functionality</li>
                  <li>Analyze usage patterns to enhance our tools</li>
                </ul>
              </section>

              <section class="policy-section">
                <h2>3. Data Storage and Security</h2>
                <p>Your card designs and preferences are stored locally in your browser using localStorage. This means:</p>
                <ul>
                  <li>Your data never leaves your device</li>
                  <li>We cannot access your personal card designs</li>
                  <li>You have full control over your data</li>
                  <li>Clearing your browser data will remove all stored information</li>
                </ul>
              </section>

              <section class="policy-section">
                <h2>4. Third-Party Services</h2>
                <p>Our website uses the following third-party services:</p>
                <ul>
                  <li><strong>Google Analytics:</strong> For anonymous website usage analytics and performance monitoring</li>
                  <li><strong>Web Hosting:</strong> For website hosting and content delivery</li>
                  <li><strong>Font Libraries:</strong> Google Fonts and other font services for card text rendering</li>
                  <li><strong>Icon Libraries:</strong> FontAwesome for user interface icons</li>
                  <li><strong>CDN Services:</strong> Content delivery networks for faster loading of static assets</li>
                </ul>
                <p>These services have their own privacy policies and we encourage you to review them. We do not share any personal information with these services beyond what is automatically collected through standard web technologies.</p>
              </section>

              <section class="policy-section">
                <h2>5. Cookies and Local Storage</h2>
                <p>We use browser local storage to:</p>
                <ul>
                  <li>Remember your language preference</li>
                  <li>Save your card designs temporarily</li>
                  <li>Improve your user experience</li>
                </ul>
                <p>You can clear this data at any time through your browser settings.</p>
              </section>

              <section class="policy-section">
                <h2>6. Children's Privacy</h2>
                <p>Our service is suitable for all ages. We do not knowingly collect personal information from children under 13. If you are a parent or guardian and believe your child has provided us with personal information, please contact us.</p>
              </section>

              <section class="policy-section">
                <h2>7. Changes to This Policy</h2>
                <p>We may update this Privacy Policy from time to time. We will notify users of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.</p>
              </section>

              <section class="policy-section">
                <h2>8. Contact Us</h2>
                <p>If you have any questions about this Privacy Policy, please contact us at:</p>
                <ul>
                  <li><strong>Website:</strong> yugiohcardmaker.org</li>
                  <li><strong>Email:</strong> <EMAIL></li>
                </ul>
              </section>

              <div class="back-link">
                <nuxt-link to="/" class="btn btn-primary">
                  <fa :icon="['fas', 'arrow-left']" class="me-2" />
                  Back to Card Maker
                </nuxt-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import LanguageSwitcher from '~/components/LanguageSwitcher.vue'

export default {
  name: 'PrivacyPage',
  
  components: {
    LanguageSwitcher
  },
  
  head() {
    return {
      title: 'Privacy Policy - yugiohcardmaker.org',
      meta: [
        { hid: 'description', name: 'description', content: 'Privacy Policy for yugiohcardmaker.org. Learn how we protect your privacy and handle your data in our Yu-Gi-Oh! card creation tool.' },
        { hid: 'robots', name: 'robots', content: 'index, follow' },
        { hid: 'og:title', property: 'og:title', content: 'Privacy Policy - yugiohcardmaker.org' },
        { hid: 'og:description', property: 'og:description', content: 'Privacy Policy for yugiohcardmaker.org Yu-Gi-Oh! card maker.' }
      ]
    }
  }
}
</script>

<style scoped>
.privacy-page {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.privacy-content {
  padding-top: 120px;
  padding-bottom: 4rem;
}

.content-wrapper {
  background: var(--bg-secondary);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px var(--shadow-dark);
  border: 1px solid var(--border-color);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.last-updated {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.policy-section {
  margin-bottom: 2.5rem;
}

.policy-section h2 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--accent-primary);
}

.policy-section p {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 1rem;
}

.policy-section ul {
  color: var(--text-secondary);
  line-height: 1.7;
  padding-left: 1.5rem;
}

.policy-section li {
  margin-bottom: 0.5rem;
}

.policy-section strong {
  color: var(--text-primary);
  font-weight: 600;
}

.back-link {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .privacy-content {
    padding-top: 100px;
    padding-bottom: 2rem;
  }
  
  .content-wrapper {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
}
</style>
